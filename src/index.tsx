import { createRoot } from 'react-dom/client'
import App from './App'

import {
  browserTracingIntegration,
  init,
  replayIntegration,
} from '@sentry/react'
import {
  consoleWarningMessage,
  getIpGeoLocation,
} from './common/utils/UtilFunctions'
import { ENVIRONMENTS } from './common/utils/consts'
import { envs } from './config/envs'

const { environment, envColor } = envs

init({
  dsn: 'https://<EMAIL>/5962352',
  integrations: [
    browserTracingIntegration(),
    replayIntegration({
      maskAllText: false,
    }),
  ],
  environment,
  // Set tracesSampleRate to 1.0 to capture 100%
  // of transactions for performance monitoring.
  // We recommend adjusting this value in production
  tracesSampleRate: 1.0,
  replaysSessionSampleRate: 1.0,
  enabled: environment !== 'development',
})

// Won't work on localhost or local dev env
if (environment !== ENVIRONMENTS.development) {
  getIpGeoLocation()
}

if (environment !== ENVIRONMENTS.development) {
  consoleWarningMessage()
}

console.log(`Using config: %c${environment}`, `color:${envColor}`)

const container = document.querySelector('#root')
// biome-ignore lint/style/noNonNullAssertion: <TODO: Find solution maybe>
const root = createRoot(container!)

root.render(<App />)

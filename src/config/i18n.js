import i18n from 'i18next'
import LngDetector from 'i18next-browser-languagedetector'
import ChainedBackend from 'i18next-chained-backend'
import LocalStorageBackend from 'i18next-localstorage-backend'
import Locize from 'i18next-locize-backend'
import { locizeEditorPlugin, locizePlugin } from 'locize'
import LastUsed from 'locize-lastused'
import { initReactI18next } from 'react-i18next'
import { ASSET } from '../common/constants/Assets'
import { envs } from './envs'
import { isLite } from './lite'
const { environment } = envs

export const languages = [
  { value: 'en-US', name: 'EN', icon: ASSET.flagUS, fullName: 'English' },
  { value: 'pt-BR', name: 'PT', icon: ASSET.flagPT, fullName: 'Português' },
  { value: 'es-SV', name: 'ES', icon: ASSET.flagES, fullName: 'Español' },
]

const locizeConfig = {
  projectId: import.meta.env.VITE_LOCIZE_PROJECT_ID,
  apiKey: import.meta.env.VITE_LOCIZE_API_KEY,
  version: 'latest',
  referenceLng: 'en-US',
}

const localStorageCacheConfig = {
  // Expiration time in ms
  expirationTime:
    // Hold cache for 1 hour in production
    environment === 'production' ? 60 * 60 * 1_000 : 24 * 60 * 60 * 1_000,
}

if (environment === 'production') {
  // Pings locize every 3 minutes to communicate if there are keys
  // that are not being used in the app
  i18n.use(LastUsed)
}
// Locize editor only available in staging
if (environment !== 'production') {
  i18n
    .use(locizePlugin)
    // the default "incontext=true" query param does not work
    // it bugs out the editor and does not fetch the translations!!!
    .use(locizeEditorPlugin({ show: false, qsProp: 'edit' }))
}

i18n
  .use(LngDetector)
  .use(initReactI18next)
  .use(ChainedBackend)
  .init({
    // Language validation
    supportedLngs: languages.map((lng) => lng.value),
    fallbackLng: languages[0].value,
    // Detection config
    detection: {
      order: [
        'localStorage',
        'querystring',
        'navigator',
        'cookie',
        'sessionStorage',
        'htmlTag',
        'path',
        'subdomain',
      ],
      caches: ['localStorage', 'sessionStorage', 'cookie'],
    },

    // Detection config
    // Backend config
    backend: {
      backends: [LocalStorageBackend, Locize],
      backendOptions: [localStorageCacheConfig, locizeConfig],
    },
    locizeLastUsed: { ...locizeConfig },
    // Other config
    ns: isLite ? 'lite' : 'default',
    react: {
      bindI18n: 'languageChanged editorSaved',
    },
    interpolation: {
      escapeValue: false,
    },
  })

export default i18n

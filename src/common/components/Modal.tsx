import { useRef } from 'react'
import { createPortal } from 'react-dom'
import { track } from '../analytics/Analytics'
import { ButtonEvent } from '../analytics/EventData'
import { ASSET } from '../constants/Assets'
import { useDetectOutsideClick } from '../hooks/useDetectOutsideClick'
import style from '../style/Modal.module.scss'
import { Backdrop, ModalProps } from '../types/Modal.types'
import Icon from './Icon'

/**
 * Wrapper used for rendering a component as a modal
 */
const Modal = ({
  children,
  isOpen,
  backdrop,
  hasOnlyContent,
  onOutsideModalContentClick,
  showCloseButton,
  onClickCloseButton,
  wide,
  backdropType,
  variant,
  dataTestID,
  className,
  customStyle,
  trackActivityBackDrop,
  trackActivityCloseButton,
}: ModalProps): JSX.Element => {
  const modalContentRef = useRef<null | HTMLDivElement>(null)

  const _onOutsideModalContentClick = () => {
    onOutsideModalContentClick?.()

    void track({
      event: ButtonEvent.clicked,
      properties: {
        object_id: trackActivityCloseButton?.trackId,
      },
    })
  }

  useDetectOutsideClick(
    modalContentRef,
    onOutsideModalContentClick ? _onOutsideModalContentClick : undefined
  )

  return isOpen ? (
    createPortal(
      <main
        className={`${className ?? ''} 
        ${
          style[
            `modal${backdrop ? `${createBackdrop(backdrop, backdropType)}` : ''}`
          ]
        }`}
        data-testid={dataTestID}
      >
        <section
          className={`${style[`modal__content${variant ? `--${variant}` : ''}`]} 
          ${customStyle?.[`${className}__content`] ?? ''}`}
        >
          <section
            className={`${style[`modal__inner`]} 
          ${customStyle?.[`${className}__inner`] ?? ''}`}
          >
            {showCloseButton && onClickCloseButton && (
              <Icon
                fileName={ASSET.iconmtcloequare}
                className={style[`modal__close-icon`]}
                onClick={() => {
                  onClickCloseButton()
                  void track({
                    event: ButtonEvent.clicked,
                    properties: {
                      object_id: trackActivityBackDrop?.trackId,
                    },
                  })
                }}
              />
            )}
            <div
              ref={modalContentRef}
              className={`${hasOnlyContent ? '' : style[`modal__content-wrapper`]} 
              ${wide ? style['modal__content-wrapper--wide'] : ''} 
              ${customStyle?.[`${className}__content-wrapper`] ?? ''}`}
            >
              {children}
            </div>
          </section>
        </section>
      </main>,
      document.body
    )
  ) : (
    <></>
  )
}

/**
 * Enables a backdrop for the modal
 */
const createBackdrop = (backdrop: boolean, backdropType?: Backdrop) => {
  if (backdrop && backdropType) {
    return `--backdrop${backdropType ? `-${backdropType}` : ''}`
  }

  return '--backdrop'
}

export default Modal

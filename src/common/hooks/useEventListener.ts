import { useEffect, useRef } from 'react'
import { UseEventListenerParams } from '../types/UseEventListener.types'

/**
 * Listens for a DOM event by passing in the event name via
 * `eventName` argument. The hook can also attach an event listener to a DOM
 * element if a DOM element is passed in as `element`
 */
export const useEventListener = ({
  eventName,
  handler,
  element = window,
}: UseEventListenerParams) => {
  // Create a ref that stores handler
  const savedHandler = useRef<((event: Event) => void) | undefined>()

  // Update ref.current value if handler changes. This allows our effect below
  // to always get latest handler ... ... without us needing to pass it in
  // effect deps array ... ... and potentially cause effect to re-run every
  // render.
  useEffect(() => {
    savedHandler.current = handler
  }, [handler])

  useEffect(
    () => {
      // Get the actual element from ref if needed
      const targetElement = 'current' in element ? element.current : element

      // Make sure element supports addEventListener
      const isSupported =
        targetElement && typeof targetElement.addEventListener === 'function'
      if (!isSupported) return

      // Create event listener that calls handler function stored in ref
      const eventListener = (event: Event) => savedHandler.current?.(event)

      // Add event listener
      targetElement.addEventListener(eventName, eventListener)

      // Remove event listener on cleanup
      return () => {
        targetElement.removeEventListener(eventName, eventListener)
      }
    },
    [eventName, element] // Re-run if eventName or element changes
  )
}

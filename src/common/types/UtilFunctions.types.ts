import dayjs from 'dayjs'
import { BackendErrorId } from '../constants/ApiErrors.types'
import { FilterTypeToNumber } from '../utils/consts'
import { RangeType } from './Filter.types'

type DateDifferenceValues = {
  years: number
  months: number
  days: number
  targetDateParsed: dayjs.Dayjs
  startDateParsed: dayjs.Dayjs
}

type ErrorNested = {
  message: string
}

type ErrorData = {
  id?: BackendErrorId
  message?: string
  nested?: ErrorNested
  data: {
    id: BackendErrorId
    message: string
    nested: ErrorNested
  }
}

type ErrorResponse = {
  id: BackendErrorId
  translatedError: string
  apiErrorMessage: string | object
  data: {
    id: BackendErrorId
    message: string
    nested: ErrorNested
    data: ErrorData
  }
}

type ApiError = {
  code: BackendErrorId
  message: string
  response: ErrorResponse
}

type MonthFormatting =
  | 'numeric'
  | '2-digit'
  | 'long'
  | 'short'
  | 'narrow'
  | undefined

type RangeNumbers = Exclude<RangeType, 'customRange'>

type FilterArrayByDateRangeParams<T> = {
  arrayOfObjects: Array<T>
  objectKey: string
  fromDate: string
  toDate: string
}

type RangeTypeToRange = {
  rangeType: RangeNumbers
  dateType: 'day'
  filterTypes: typeof FilterTypeToNumber
}

export type {
  DateDifferenceValues,
  ApiError,
  MonthFormatting,
  RangeTypeToRange,
  FilterArrayByDateRangeParams,
  RangeNumbers,
}

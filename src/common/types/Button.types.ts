import { HTMLAttributes } from 'react'
import { TrackActivity } from './CommonTypes.types'

type ButtonVariant =
  | 'primary'
  | 'secondary'
  | 'alternative'
  | 'login'
  | 'loading'
  | 'logout'
  | 'back'
  | 'back--light'
  | 'signup-desktop'
  | 'danger'
  | 'blue'
  | 'select-value'
  | 'text-only'
  | 'yellow'
  | 'blue-dark'
  | 'gray-dark'
  //TODO: Will add more when needed and use template literals
  | 'primary--animated'

interface ButtonProps extends HTMLAttributes<HTMLButtonElement> {
  disabled?: boolean
  children?: JSX.Element | string
  trackActivity?: TrackActivity
  className?: string
  icon?: string
  /**
   * Might get deprecated
   */
  textOnLoading?: string
  loading?: boolean
  dataTestID?: string
  loadingAnimation?: object
  /**
   * Default value for variant is `primary`
   */
  variant?: ButtonVariant
}

export type { ButtonVariant, ButtonProps }

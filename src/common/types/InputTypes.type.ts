import React, {
  DetailedHTMLProps,
  HTMLInputTypeAttribute,
  InputHTMLAttributes,
  KeyboardEvent,
  MouseEvent,
} from 'react'
import { TrackActivity, ValidationData } from './CommonTypes.types'

type InputProps = DetailedHTMLProps<
  InputHTMLAttributes<HTMLInputElement>,
  HTMLInputElement
>

interface UserInputProps<T> {
  value?: T
  onChange?: (value: T) => void
  onClickSuffixIcon?: () => void
  onClick?: (event: MouseEvent<HTMLInputElement>) => void
  type?: HTMLInputTypeAttribute
  label?: string
  placeholder?: string
  maxLength?: number
  minLength?: number
  id?: string
  onKeyDown?: (event: KeyboardEvent<HTMLInputElement>) => void
  readOnly?: boolean
  inputMode?:
    | 'numeric'
    | 'search'
    | 'text'
    | 'email'
    | 'tel'
    | 'url'
    | 'none'
    | 'decimal'
    | undefined
  max?: number
  min?: number
  pattern?: string
  className?: string
  suffixIcon?: string
  errorMessage?: ValidationData
  jsxValue?: string | React.ReactNode
  defaultValue?: string | number | readonly string[] | undefined
  prefix?: string
  suffixText?: string
  dataTestID?: string
  autoComplete?: string
  labelInfoIcon?: boolean
  isTextArea?: boolean
  restrictionRegex?: RegExp
  optional?: boolean
  tooltipText?: string
  height?: string
  alternateLabel?: boolean
  trackActivity?: TrackActivity
}

type ToggleProps = {
  className?: string
  label: string | React.JSX.Element
  onChange?: () => void
  toggled: boolean
  testID?: string
  trackActivity?: TrackActivity
  icon?: React.JSX.Element
}

export type { InputProps, ToggleProps, UserInputProps }

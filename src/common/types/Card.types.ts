import { ReactNode } from 'react'
import { ButtonVariant } from './Button.types'

// If both Card and it's children should have variant enter here, else enter individually
const baseVariants = {
  box: 'box',
  boxAlt: 'box-alt',
} as const

export const cardVariants = {
  ...baseVariants,
  stat: 'stat',
  feedback: 'feedback',
  grayDirty: 'gray-dirty',
  blueFaint: 'blue-faint',
} as const

export const cardHeaderVariants = {
  ...baseVariants,
} as const

export const cardBodyFooterVariants = {
  ...baseVariants,
  feedback: 'feedback',
} as const

type CardVariantType = (typeof cardVariants)[keyof typeof cardVariants]
type CardHeaderVariantType =
  (typeof cardHeaderVariants)[keyof typeof cardHeaderVariants]
type CardBodyFooterVariantType =
  (typeof cardBodyFooterVariants)[keyof typeof cardBodyFooterVariants]

type CardAlertType = number | 'completed' | 'warn' | 'error' | 'pending'

interface CardArrowAndAlertProps {
  alert?: CardAlertType
  showArrow?: boolean
  alertAndArrowPosition?: 'start' | 'end'
  rotateArrow?: 'down' | 'up'
  arrowInvisible?: boolean
}

interface CardRoundness {
  roundness?: 'off' | 'rounded-sm' | 'rounded' | 'rounded-l'
}

interface CardHeaderProps extends CardArrowAndAlertProps {
  headerImage?: string
  headerImageSize?: 'small' | 'large' | 'x-large'
  variant?: CardHeaderVariantType
  secondaryIcon?: string
}

interface CardBodyProps {
  title: string | JSX.Element
  subTitle?: string
  variant?: CardBodyFooterVariantType
}

interface CardFooterProps extends CardArrowAndAlertProps {
  variant?: CardBodyFooterVariantType
  ctaButtonLabel?: string
  ctaButtonVariant?: ButtonVariant
  onClick?: () => void
  secondaryIcon?: string
  extendFooter?: ReactNode
}

type CardProps = Omit<CardHeaderProps, 'variant'> &
  Omit<CardBodyProps, 'variant'> &
  Omit<CardFooterProps, 'variant'> &
  CardRoundness & {
    active?: boolean
    interactEnabled?: boolean
    disabled?: boolean
    dataTestID?: string
    variant?: CardVariantType
    className?: string
  }

interface ExtendedContentCardProps extends CardProps {
  children: JSX.Element
  autoExpand?: boolean
  expandClickDisabled?: boolean
  extendedCardVariant?: 'payout'
  className?: string
}

export type {
  CardAlertType,
  CardArrowAndAlertProps,
  CardBodyFooterVariantType,
  CardBodyProps,
  CardFooterProps,
  CardHeaderProps,
  CardHeaderVariantType,
  CardProps,
  CardVariantType,
  ExtendedContentCardProps,
}

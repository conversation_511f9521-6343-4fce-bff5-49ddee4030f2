import LottieAnimation from '../../../common/components/LottieAnimation'
import { useDrawTontinator } from '../hooks/useDrawTontinator'
import { useGraphResize } from '../hooks/useGraphResize'
import { TontinatorLineChartProps } from '../types/Visualization.types'

/**
 * Uses the `useDrawTontinator` hook to draw the tontinator graph. This
 * component just provides the necessary `svg` container for the hook to start
 * appending svg elements for the chart.
 *
 * If data is being fetched or a window resize is detected, a lottie animation
 * will be rendered until a resize or data fetch operation is completed.
 */
const TontinatorLineChart = ({
  width,
  height,
  containerCssClass,
  drawingAnimation,
  forecastData,
  xAxisCssClass,
  yAxisCssClass,
  tontineLineKey,
  fixedAnnuityLineKey,
  bankDepositKey,
  tontineLineStyle,
  bankDepositsStyle,
  fixedAnnuityStyle,
  numOfTicksForX,
  numOfTicksForY,
  axisDistanceFromGraph,
  mainSVGContainerID,
  toggles,
  showVerticalHoveringLine,
  formatter,
  isLoading,
  showFocusCircleOnPath,
  showHoveringMouseAnnotation,
  legendData,
  multipleTontineLineStyles,
  onHoverOrTapLeave,
  onHoverOrTapStart,
}: TontinatorLineChartProps): JSX.Element => {
  const draw = useGraphResize()

  useDrawTontinator({
    width,
    height,
    mainSVGContainerID,
    containerCssClass,
    drawingAnimation,
    redraw: draw,
    forecastData,
    xAxisCssClass,
    yAxisCssClass,
    tontineLineKey,
    fixedAnnuityLineKey,
    bankDepositKey,
    tontineLineStyle,
    bankDepositsStyle,
    fixedAnnuityStyle,
    numOfTicksForX,
    numOfTicksForY,
    axisDistanceFromGraph,
    toggles,
    showVerticalHoveringLine,
    formatter,
    isLoading,
    showFocusCircleOnPath,
    showHoveringMouseAnnotation,
    legendData,
    multipleTontineLineStyles,
    onHoverOrTapLeave,
    onHoverOrTapStart,
  })

  if (!isLoading && forecastData?.length > 0) {
    return (
      <main className={containerCssClass}>
        <svg id={mainSVGContainerID} />
      </main>
    )
  }

  return (
    <main className={containerCssClass}>
      <LottieAnimation
        style={{
          width: '100%',
          height: '100%',
        }}
        animationName={drawingAnimation}
        autoplay
        loop
      />
    </main>
  )
}

export default TontinatorLineChart

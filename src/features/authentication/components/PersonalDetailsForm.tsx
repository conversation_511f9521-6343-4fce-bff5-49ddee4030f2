import DateOfBirthInput from '../../../common/components/DateOfBirthInput'
import InputGroup from '../../../common/components/InputGroup'
import TextInput from '../../../common/components/TextInput'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { INPUT_LIMIT } from '../../../common/constants/InputLimits'
import { inputRestrictionRegex } from '../../../common/constants/Regex'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { INPUT_TYPE } from '../../../common/utils/consts'
import { useAccountService } from '../hooks/useAccountService'
import style from '../style/PersonalDetails.module.scss'
import { PersonalDetailsFormProps } from '../types/PersonalDetailsForm.types'
import AddPhoneNumber from './AddPhoneNumber'

const PersonalDetailsForm = ({
  firstName,
  setFirstName,
  lastName,
  setLastName,
  userDob,
  setUserDob,
  userIsTyping,
  isReadOnly,
  saveEditedDetails,
  navigateToBiometrics,
  inputValidation: {
    firstNameValidated,
    validateFirstName,
    lastNameValidated,
    validateLastName,
  },
}: PersonalDetailsFormProps) => {
  const t = useTranslate()

  const {
    context: { user_details },
  } = useAccountService()

  return (
    <InputGroup>
      <TextInput
        value={firstName}
        onChange={setFirstName}
        label={t('INPUT_LABEL.SIGN_UP_FIRSTNAME')}
        placeholder={user_details?.unverified_first_name}
        errorMessage={firstNameValidated}
        dataTestID={UI_TEST_ID.firstNameInput}
        maxLength={INPUT_LIMIT.GENERIC_MAX}
        restrictionRegex={inputRestrictionRegex.names}
        validatorFunction={validateFirstName}
        optional
        readOnly={isReadOnly}
      />

      <TextInput
        value={lastName}
        onChange={setLastName}
        label={t('INPUT_LABEL.SIGN_UP_LASTNAME')}
        placeholder={user_details?.unverified_last_name}
        errorMessage={lastNameValidated}
        dataTestID={UI_TEST_ID.lastNameInput}
        maxLength={INPUT_LIMIT.GENERIC_MAX}
        restrictionRegex={inputRestrictionRegex.names}
        validatorFunction={validateLastName}
        optional
        readOnly={isReadOnly}
      />

      <DateOfBirthInput
        value={typeof userDob === 'string' ? userDob : ''}
        onChange={setUserDob}
        label={t('INPUT_LABEL.DATE_OF_BIRTH')}
        readOnly={isReadOnly}
      />

      <AddPhoneNumber
        className={style['personalDetails__phone-input']}
        userIsTyping={userIsTyping}
        navigateToBiometrics={navigateToBiometrics}
        saveEditedDetails={saveEditedDetails}
      />

      <TextInput
        value={user_details?.email}
        label={t('ACCOUNT.PAGE_TITLE_EMAIL')}
        readOnly
        placeholder={user_details?.email}
        type={INPUT_TYPE.EMAIL}
        dataTestID={UI_TEST_ID.emailInput}
        optional
      />
    </InputGroup>
  )
}

export default PersonalDetailsForm

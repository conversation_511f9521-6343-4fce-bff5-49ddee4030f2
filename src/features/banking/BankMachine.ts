import dayjs from 'dayjs'
import { and, assign, fromPromise, setup } from 'xstate'
import { promiseActorInput } from '../StateUtils'
import { TontineProduct } from '../agreements/types/LegalMachineTypes.types'
import {
  AuthMachineEvent,
  InvestmentDetails,
} from '../authentication/types/AuthMachineTypes.type'
import {
  isAuthenticated,
  isInvestmentAccountOpen,
  isLiteBuild,
} from './BankMachineGuards'
import { getReturns, readUserBankingInfo } from './BankMachineServices'
import {
  BankMachineContext,
  Events,
  ForecastRules,
} from './types/BankMachineTypes.type'
import { HistoryTransaction } from './types/BankTypes.type'
import { BANKING_CONSTANTS } from './utils/consts'

/**
 * Bank machine configuration
 */
export const bankMachine = setup({
  types: {
    context: {} as BankMachineContext,
    events: {} as Events,
  },
  actors: {
    readUserBankingInfo: fromPromise(
      async ({
        input,
      }: {
        input: { context: BankMachineContext; event: AuthMachineEvent }
      }) => readUserBankingInfo(input?.context, input?.event)
    ),
    getReturnsActor: fromPromise(
      async ({
        input,
      }: {
        input: { context: BankMachineContext; event: AuthMachineEvent }
      }) => getReturns(input?.context, input?.event)
    ),
  },
  actions: {
    updateReturns: assign({
      returns: ({ event }) => {
        return event?.output as unknown as {
          returns: InvestmentDetails
          forecastRules: ForecastRules
        }
      },
    }),

    wipeBankFetchError: assign({
      bankingInfoError: undefined,
    }),

    changeProduct: assign({
      tontineProduct: ({ event }) => {
        return event?.payload?.product as TontineProduct
      },
    }),

    storeBankingInfo: assign({
      bankingInfo: ({ event }) => {
        const payinHistory = event?.output?.nominalBalance
          ?.filter((balance) => {
            //@ts-ignore
            return (
              balance?.transaction?.[`type'`] === BANKING_CONSTANTS.contribution
            )
          })
          .sort((a, b) =>
            dayjs((b as HistoryTransaction).transaction?.time).diff(
              dayjs((a as HistoryTransaction).transaction?.time)
            )
          ) as Array<HistoryTransaction>

        const payoutHistory = event?.output?.nominalBalance
          ?.filter((balance) => {
            //@ts-ignore
            return balance?.transaction?.[`type'`] === BANKING_CONSTANTS.payout
          })
          .sort((a, b) =>
            dayjs((b as HistoryTransaction).transaction?.time).diff(
              dayjs((a as HistoryTransaction).transaction?.time)
            )
          ) as Array<HistoryTransaction>

        const all = [...payinHistory, ...payoutHistory].sort((a, b) =>
          dayjs(b.transaction?.time).diff(dayjs(a.transaction?.time))
        )

        return {
          ...event?.output,
          payinHistory,
          payoutHistory,
          all,
        }
      },
    }),
  },
  guards: {
    isLiteBuild,
    isAuthenticated,
    isInvestmentAccountOpen: ({ event }) =>
      isInvestmentAccountOpen({ event } as unknown as {
        event: AuthMachineEvent
      }) as boolean,
  },
}).createMachine({
  //@ts-ignore
  context: {
    bankingInfo: {
      nominalBalance: [],
      payoutHistory: [],
      payinHistory: [],
      nextPayout: null,
      all: [],
    },
    bankingInfoError: undefined,
    tontineProduct: 'TontineTrustFund',
  },
  id: 'BankMachine',
  initial: 'IDLE',
  states: {
    IDLE: {
      on: {
        UPDATE_PRODUCT: {
          actions: {
            type: 'changeProduct',
          },
        },
        FETCH_BANKING_INFO: {
          target: 'FETCHING_BANK_INFO',
        },
        GET_RETURNS: {
          target: 'GETTING_RETURNS',
        },
      },
    },
    GETTING_RETURNS: {
      invoke: {
        src: 'getReturnsActor',
        id: 'getReturnsActorID',
        //@ts-ignore
        input: promiseActorInput,
        onDone: [
          {
            guard: 'isLiteBuild',
            target: 'IDLE',
            actions: ['updateReturns'],
          },
          {
            guard: and(['isAuthenticated', 'isInvestmentAccountOpen']),
            target: 'FETCHING_BANK_INFO',
            actions: ['updateReturns'],
          },
          {
            target: 'IDLE',
            actions: ['updateReturns'],
          },
        ],
        onError: {
          target: 'IDLE',
        },
      },
    },

    FETCHING_BANK_INFO: {
      invoke: {
        src: 'readUserBankingInfo',
        id: 'readUserBankingInfoID',
        //@ts-ignore
        input: promiseActorInput,
        onError: {
          target: 'IDLE',
        },
        onDone: {
          target: 'IDLE',
          actions: ['storeBankingInfo', 'wipeBankFetchError'],
        },
      },
      description:
        'Makes a parallel request to multiple BS APIs to fetch all banking information',
    },
  },
})

import { isLite } from '../../config/lite'
import { getAuthToken } from '../authentication/utils/AuthFunctions'
import { BankMachineEvent } from './types/BankMachineTypes.type'

const isLiteBuild = () => isLite

const isAuthenticated = () => Boolean(getAuthToken())

/**
 * Checks if the user has an investment account open before fetching their
 * balance history
 */
const isInvestmentAccountOpen = ({ event }: { event: BankMachineEvent }) => {
  return event?.output?.invAccOpen ?? event?.payload?.invAccOpen
}

export { isAuthenticated, isInvestmentAccountOpen, isLiteBuild }

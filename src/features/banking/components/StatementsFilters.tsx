import Dropdown from '../../../common/components/Dropdown'
import style from '../style/StatementsFilters.module.scss'
import { StatementsFiltersProps } from '../types/StatementsFilters.types'
import { statementTypes } from '../utils/consts'

/**
 * Dropdown filters used for filtering statement types and statuses
 */
const StatementsFilters = <T extends (typeof statementTypes)[number]>({
  statementType,
  onStatementTypeChange,
  statementOptions,
  status,
  onStatusChange,
  statusOptions,
  statementTypeLabel,
  statusLabel,
}: StatementsFiltersProps<T>) => {
  return (
    <section className={style['statements-filters']}>
      <Dropdown
        className={style['statements-filters__type']}
        optional
        label={statementTypeLabel}
        searchBy={['label']}
        itemKey={{
          displayKey: 'label',
        }}
        value={statementType}
        options={statementOptions}
        onChange={(option) => onStatementTypeChange(option)}
      />
      {statusOptions && statusOptions?.length > 0 && (
        <Dropdown
          optional
          label={statusLabel}
          searchBy={['label']}
          itemKey={{
            displayKey: 'label',
          }}
          value={status}
          options={statusOptions}
          onChange={(option) => onStatusChange?.(option as T)}
        />
      )}
    </section>
  )
}

export default StatementsFilters

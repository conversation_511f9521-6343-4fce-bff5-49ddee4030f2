import RangeSlider from '../../../src/common/components/RangeSlider'

describe('RangeSlider.jsx', () => {
  const steps = [0, 25, 50, 75, 100]

  it('renders without crashing', () => {
    const onChange = cy.stub().as('onChange')
    cy.mount(<RangeSlider value={0} onChange={onChange} />)
  })

  it('calls the onChange function and sets the value to the next value from the steps', () => {
    const onChange = cy.stub().as('onChange')

    cy.mount(<RangeSlider value={0} onChange={onChange} steps={steps} />)
    //Increment arrow
    cy.get('.sliderInput__container > :nth-child(3)').click()
    // Verify that the onChange function has been called with the expected value
    cy.get('@onChange').should('have.been.calledWith', 25)
  })

  it('calls the onChange function when slider value box is clicked', () => {
    const onChange = cy.stub().as('onChange')

    cy.mount(<RangeSlider value={0} onChange={onChange} steps={steps} />)
    //Middle box value
    cy.get('.sliderInput__value').click()
    // Verify that the onChange function has been called with the expected value
    cy.get('@onChange').should('have.been.calledWith', 25)
  })

  it('calls the onChange function when the decrement arrow is clicked', () => {
    const onChange = cy.stub().as('onChange')

    cy.mount(<RangeSlider value={25} onChange={onChange} steps={steps} />)
    cy.get('.sliderInput__container > :nth-child(1)').click()
    // Verify that the onChange function has been called with the expected value
    cy.get('@onChange').should('have.been.calledWith', 0)
  })
})

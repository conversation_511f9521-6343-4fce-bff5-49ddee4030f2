import PinConfirmation from '../../../../src/features/authentication/components/PinConfirmation'

import { mount } from 'cypress/react'
import { TESTING_IDS } from '../../../support/ui-component-ids'

const PIN_LENGTH = 4
const TEST_PIN = ['1', '2', '3', '4']

describe('PinConfirmation.tsx', () => {
  let handleSubmitSpy: Cypress.Agent<sinon.SinonSpy>
  let errorCallbackSpy: Cypress.Agent<sinon.SinonSpy>

  beforeEach(() => {
    handleSubmitSpy = cy.spy().as('handleSubmit')
    errorCallbackSpy = cy.spy().as('errorCallback')
  })

  const mountComponent = () => {
    mount(
      <PinConfirmation
        headerTitle="Set your PIN"
        initialPinLabel="Enter PIN"
        confirmPinLabel="Confirm PIN"
        handleSubmit={handleSubmitSpy}
        errorCallback={errorCallbackSpy}
        pinLength={PIN_LENGTH}
      />
    )
  }

  const enterPin = (label: string, pin: string[]) => {
    cy.contains(label)
      .parent()
      .find('input')
      .each(($input, index) => {
        cy.wrap($input).type(pin[index])
      })
  }

  it('should render both PIN input fields', () => {
    mountComponent()
    cy.contains('Enter PIN').should('exist')
    cy.contains('Confirm PIN').should('exist')
  })

  it('should focus confirm pin input after initial pin is filled', () => {
    mountComponent()

    enterPin('Enter PIN', TEST_PIN)

    cy.contains('Confirm PIN')
      .parent()
      .find('input')
      .first()
      .should('have.focus')
  })

  it('should call handleSubmit on matching PINs', () => {
    mountComponent()

    enterPin('Enter PIN', TEST_PIN)
    enterPin('Confirm PIN', TEST_PIN)

    cy.get('@handleSubmit').should('have.been.calledOnce')
  })

  it('should reset and show error when PINs do not match', () => {
    mountComponent()

    enterPin('Enter PIN', TEST_PIN)
    enterPin('Confirm PIN', ['4', '3', '2', '1'])

    cy.get('@handleSubmit').should('not.have.been.called')
    cy.get('@errorCallback').should('have.been.calledOnce')
    cy.contains('Confirm PIN')
      .parent()
      .find('input')
      .first()
      .should('have.value', '')
    cy.contains('Enter PIN')
      .parent()
      .find('input')
      .first()
      .should('have.value', '')
    cy.getByDataID(TESTING_IDS.pinConfirmationError).should('exist')
  })

  it('should focus initial input again after error', () => {
    mountComponent()

    enterPin('Enter PIN', TEST_PIN)
    enterPin('Confirm PIN', ['9', '9', '9', '9'])

    cy.get('@errorCallback').should('have.been.called')
    cy.contains('Enter PIN').parent().find('input').first().should('have.focus')
  })
})
